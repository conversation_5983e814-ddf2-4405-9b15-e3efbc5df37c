import 'dart:convert';

/// Utility class for parsing structured response text into organized sections
class ResponseTextParser {
  /// Parses the structured response text and returns organized sections
  static Map<String, dynamic> parseStructuredResponse(String responseText) {
    try {
      // Clean the response text by removing escape characters and extra quotes
      String cleanedText = responseText
          .replaceAll('\\"', '"')
          .replaceAll('\\n', '\n')
          .trim();
      
      // If the text starts and ends with quotes, remove them
      if (cleanedText.startsWith('"') && cleanedText.endsWith('"')) {
        cleanedText = cleanedText.substring(1, cleanedText.length - 1);
      }

      Map<String, dynamic> parsedData = {
        'sections': <Map<String, dynamic>>[],
        'recommendations': <String>[],
        'contentBlocks': <Map<String, dynamic>>[],
        'rawText': cleanedText,
        'hasStructuredSections': false,
      };

      // Split the text into lines for processing
      List<String> lines = cleanedText.split('\n');
      
      Map<String, dynamic>? currentSection;
      List<String> currentPoints = [];
      List<String> beforeContent = [];
      List<String> afterContent = [];
      String currentSubsection = '';
      bool inStructuredSection = false;
      bool collectingAfterContent = false;

      for (int i = 0; i < lines.length; i++) {
        String trimmedLine = lines[i].trim();
        
        if (trimmedLine.isEmpty) continue;

        // Check if this is a main section (numbered like "1. **DOMAIN INTELLIGENCE**")
        RegExp mainSectionRegex = RegExp(r'^(\d+)\.\s*\*\*([^*]+)\*\*');
        Match? mainSectionMatch = mainSectionRegex.firstMatch(trimmedLine);
        
        if (mainSectionMatch != null) {
          // If we were collecting after content, save it to the previous section
          if (collectingAfterContent && currentSection != null) {
            currentSection['afterContent'] = List<String>.from(afterContent);
            afterContent.clear();
            collectingAfterContent = false;
          }
          
          inStructuredSection = true;
          parsedData['hasStructuredSections'] = true;
          
          // Save previous section if exists
          if (currentSection != null) {
            if (currentPoints.isNotEmpty) {
              currentSection['subsections'][currentSubsection] = List<String>.from(currentPoints);
              currentPoints.clear();
            }
            parsedData['sections'].add(Map<String, dynamic>.from(currentSection));
          }
          
          // Start new section
          currentSection = {
            'number': mainSectionMatch.group(1),
            'title': mainSectionMatch.group(2)?.trim(),
            'subsections': <String, List<String>>{},
            'order': parsedData['sections'].length,
            'beforeContent': List<String>.from(beforeContent),
            'afterContent': <String>[],
          };
          
          // Clear before content for next section
          beforeContent.clear();
          currentSubsection = '';
          continue;
        }

        // Check if this is a subsection (like "- Unable to detect specific domain...")
        if (trimmedLine.startsWith('-') && currentSection != null) {
          // Save previous subsection points if any
          if (currentSubsection.isNotEmpty && currentPoints.isNotEmpty) {
            currentSection['subsections'][currentSubsection] = List<String>.from(currentPoints);
            currentPoints.clear();
          }
          
          // Start new subsection
          currentSubsection = trimmedLine.substring(1).trim();
          continue;
        }

        // Check if this is a recommendation (like "Recommendations:")
        if (trimmedLine.toLowerCase().contains('recommendation')) {
          currentSubsection = 'recommendations';
          continue;
        }

        // Check if this is a numbered recommendation (like "1. Define your main product...")
        RegExp recommendationRegex = RegExp(r'^(\d+)\.\s*(.+)');
        Match? recommendationMatch = recommendationRegex.firstMatch(trimmedLine);
        
        if (recommendationMatch != null && currentSubsection == 'recommendations') {
          parsedData['recommendations'].add(recommendationMatch.group(2)?.trim() ?? '');
          continue;
        }

        // Regular content line
        if (currentSection != null && trimmedLine.isNotEmpty) {
          // Check if this might be content after the section (look ahead for next section)
          bool isAfterContent = false;
          for (int j = i + 1; j < lines.length; j++) {
            String nextLine = lines[j].trim();
            if (nextLine.isEmpty) continue;
            
            RegExp nextSectionRegex = RegExp(r'^(\d+)\.\s*\*\*([^*]+)\*\*');
            if (nextSectionRegex.hasMatch(nextLine)) {
              isAfterContent = true;
              collectingAfterContent = true;
              break;
            }
            
            // If we find content that belongs to current section, it's not after content
            if (nextLine.startsWith('-') || nextLine.toLowerCase().contains('recommendation')) {
              break;
            }
          }
          
          if (isAfterContent) {
            afterContent.add(trimmedLine);
          } else {
            // Add to current section points
            currentPoints.add(trimmedLine);
          }
        } else if (!inStructuredSection && trimmedLine.isNotEmpty) {
          // Add to before content if we haven't encountered structured sections yet
          beforeContent.add(trimmedLine);
        } else if (inStructuredSection && currentSection == null && trimmedLine.isNotEmpty) {
          // Content between sections
          beforeContent.add(trimmedLine);
        }
      }

      // Save the last section
      if (currentSection != null) {
        if (currentPoints.isNotEmpty) {
          currentSection['subsections'][currentSubsection] = List<String>.from(currentPoints);
        }
        if (afterContent.isNotEmpty) {
          currentSection['afterContent'] = List<String>.from(afterContent);
        }
        parsedData['sections'].add(Map<String, dynamic>.from(currentSection));
      }

      // Create content blocks for easier access
      List<Map<String, dynamic>> contentBlocks = [];
      
      // Add initial content block if there's content before first section
      if (beforeContent.isNotEmpty) {
        contentBlocks.add({
          'type': 'before_sections',
          'content': beforeContent,
          'order': -1,
        });
      }
      
      // Add content blocks for each section
      for (Map<String, dynamic> section in parsedData['sections']) {
        // Before content for this section
        if (section['beforeContent'] != null && (section['beforeContent'] as List).isNotEmpty) {
          contentBlocks.add({
            'type': 'before_section',
            'sectionNumber': section['number'],
            'sectionTitle': section['title'],
            'content': section['beforeContent'],
            'order': (section['order'] as int) * 2,
          });
        }
        
        // The section itself
        contentBlocks.add({
          'type': 'section',
          'sectionNumber': section['number'],
          'sectionTitle': section['title'],
          'content': section,
          'order': (section['order'] as int) * 2 + 1,
        });
        
        // After content for this section
        if (section['afterContent'] != null && (section['afterContent'] as List).isNotEmpty) {
          contentBlocks.add({
            'type': 'after_section',
            'sectionNumber': section['number'],
            'sectionTitle': section['title'],
            'content': section['afterContent'],
            'order': (section['order'] as int) * 2 + 2,
          });
        }
      }
      
      // Add recommendations block
      if ((parsedData['recommendations'] as List).isNotEmpty) {
        contentBlocks.add({
          'type': 'recommendations',
          'content': parsedData['recommendations'],
          'order': 1000, // Always last
        });
      }
      
      // Sort content blocks by order
      contentBlocks.sort((a, b) => (a['order'] as int).compareTo(b['order'] as int));
      
      parsedData['contentBlocks'] = contentBlocks;

      return parsedData;
    } catch (e) {
      // Return error information if parsing fails
      return {
        'error': 'Failed to parse response text: $e',
        'sections': <Map<String, dynamic>>[],
        'recommendations': <String>[],
        'contentBlocks': <Map<String, dynamic>>[],
        'rawText': responseText,
        'hasStructuredSections': false,
      };
    }
  }

  /// Formats the parsed data into a readable structure
  static String formatParsedData(Map<String, dynamic> parsedData) {
    StringBuffer buffer = StringBuffer();
    
    if (parsedData.containsKey('error')) {
      buffer.writeln('Error: ${parsedData['error']}');
      return buffer.toString();
    }

    List<Map<String, dynamic>> sections = parsedData['sections'] ?? [];
    List<String> recommendations = parsedData['recommendations'] ?? [];

    buffer.writeln('=== PARSED RESPONSE DATA ===\n');

    // Format sections
    for (Map<String, dynamic> section in sections) {
      buffer.writeln('${section['number']}. ${section['title']}');
      buffer.writeln('${'=' * 50}');
      
      Map<String, List<String>> subsections = Map<String, List<String>>.from(section['subsections'] ?? {});
      
      for (String subsectionKey in subsections.keys) {
        if (subsectionKey.isNotEmpty) {
          buffer.writeln('\n📋 $subsectionKey');
          buffer.writeln('${'-' * 30}');
        }
        
        List<String> points = subsections[subsectionKey] ?? [];
        for (int i = 0; i < points.length; i++) {
          buffer.writeln('   ${i + 1}. ${points[i]}');
        }
      }
      buffer.writeln('\n');
    }

    // Format recommendations
    if (recommendations.isNotEmpty) {
      buffer.writeln('🎯 RECOMMENDATIONS');
      buffer.writeln('${'=' * 50}');
      for (int i = 0; i < recommendations.length; i++) {
        buffer.writeln('   ${i + 1}. ${recommendations[i]}');
      }
    }

    return buffer.toString();
  }

  /// Extracts specific section by title
  static Map<String, dynamic>? getSection(Map<String, dynamic> parsedData, String sectionTitle) {
    List<Map<String, dynamic>> sections = parsedData['sections'] ?? [];
    
    for (Map<String, dynamic> section in sections) {
      if (section['title']?.toString().toLowerCase().contains(sectionTitle.toLowerCase()) == true) {
        return section;
      }
    }
    return null;
  }

  /// Extracts all points from a specific section
  static List<String> getSectionPoints(Map<String, dynamic> parsedData, String sectionTitle) {
    Map<String, dynamic>? section = getSection(parsedData, sectionTitle);
    if (section == null) return [];

    List<String> allPoints = [];
    Map<String, List<String>> subsections = Map<String, List<String>>.from(section['subsections'] ?? {});
    
    for (List<String> points in subsections.values) {
      allPoints.addAll(points);
    }
    
    return allPoints;
  }

  /// Converts parsed data to JSON string
  static String toJson(Map<String, dynamic> parsedData) {
    try {
      return JsonEncoder.withIndent('  ').convert(parsedData);
    } catch (e) {
      return '{"error": "Failed to convert to JSON: $e"}';
    }
  }

  /// Creates a summary of the parsed data
  static Map<String, dynamic> createSummary(Map<String, dynamic> parsedData) {
    List<Map<String, dynamic>> sections = parsedData['sections'] ?? [];
    List<String> recommendations = parsedData['recommendations'] ?? [];
    List<String> otherContent = parsedData['otherContent'] ?? [];

    Map<String, dynamic> summary = {
      'totalSections': sections.length,
      'totalRecommendations': recommendations.length,
      'totalOtherContent': otherContent.length,
      'sectionTitles': [],
      'hasError': parsedData.containsKey('error'),
      'hasStructuredSections': parsedData['hasStructuredSections'] ?? false,
    };

    for (Map<String, dynamic> section in sections) {
      summary['sectionTitles'].add(section['title']);
    }

    return summary;
  }

  /// Gets sections in sequential order
  static List<Map<String, dynamic>> getSectionsInOrder(Map<String, dynamic> parsedData) {
    List<Map<String, dynamic>> sections = parsedData['sections'] ?? [];
    
    // Sort sections by their order field
    sections.sort((a, b) {
      int orderA = a['order'] ?? 0;
      int orderB = b['order'] ?? 0;
      return orderA.compareTo(orderB);
    });
    
    return sections;
  }

  /// Gets other content that appears before structured sections
  static List<String> getOtherContent(Map<String, dynamic> parsedData) {
    return parsedData['otherContent'] ?? [];
  }

  /// Checks if the response has structured sections
  static bool hasStructuredSections(Map<String, dynamic> parsedData) {
    return parsedData['hasStructuredSections'] ?? false;
  }

  /// Gets content in display order (other content first, then sections, then recommendations)
  static Map<String, dynamic> getContentInDisplayOrder(Map<String, dynamic> parsedData) {
    return {
      'otherContent': getOtherContent(parsedData),
      'sections': getSectionsInOrder(parsedData),
      'recommendations': parsedData['recommendations'] ?? [],
      'hasStructuredSections': hasStructuredSections(parsedData),
    };
  }

  /// Parses response for follow-up sections specifically
  static Map<String, dynamic> parseForFollowUpSections(String responseText) {
    Map<String, dynamic> parsedData = parseStructuredResponse(responseText);
    Map<String, dynamic> displayOrder = getContentInDisplayOrder(parsedData);
    
    return {
      'hasFollowUpSections': displayOrder['sections'].isNotEmpty || 
                            displayOrder['recommendations'].isNotEmpty,
      'otherContent': displayOrder['otherContent'],
      'sections': displayOrder['sections'],
      'recommendations': displayOrder['recommendations'],
      'hasStructuredSections': displayOrder['hasStructuredSections'],
      'contentBlocks': parsedData['contentBlocks'],
      'summary': createSummary(parsedData),
    };
  }

  /// Gets content blocks in order (before content, sections, after content, recommendations)
  static List<Map<String, dynamic>> getContentBlocks(Map<String, dynamic> parsedData) {
    return parsedData['contentBlocks'] ?? [];
  }

  /// Gets content blocks of a specific type
  static List<Map<String, dynamic>> getContentBlocksByType(Map<String, dynamic> parsedData, String type) {
    List<Map<String, dynamic>> contentBlocks = getContentBlocks(parsedData);
    return contentBlocks.where((block) => block['type'] == type).toList();
  }

  /// Gets before content for a specific section
  static List<String> getBeforeContentForSection(Map<String, dynamic> parsedData, String sectionNumber) {
    List<Map<String, dynamic>> beforeBlocks = getContentBlocksByType(parsedData, 'before_section');
    for (Map<String, dynamic> block in beforeBlocks) {
      if (block['sectionNumber'] == sectionNumber) {
        return List<String>.from(block['content'] ?? []);
      }
    }
    return [];
  }

  /// Gets after content for a specific section
  static List<String> getAfterContentForSection(Map<String, dynamic> parsedData, String sectionNumber) {
    List<Map<String, dynamic>> afterBlocks = getContentBlocksByType(parsedData, 'after_section');
    for (Map<String, dynamic> block in afterBlocks) {
      if (block['sectionNumber'] == sectionNumber) {
        return List<String>.from(block['content'] ?? []);
      }
    }
    return [];
  }

  /// Gets all content before sections (initial content)
  static List<String> getContentBeforeSections(Map<String, dynamic> parsedData) {
    List<Map<String, dynamic>> beforeBlocks = getContentBlocksByType(parsedData, 'before_sections');
    if (beforeBlocks.isNotEmpty) {
      return List<String>.from(beforeBlocks.first['content'] ?? []);
    }
    return [];
  }

  /// Creates a structured list of objects containing before and after content for each section
  static List<Map<String, dynamic>> createSectionContentObjects(Map<String, dynamic> parsedData) {
    List<Map<String, dynamic>> sectionObjects = [];
    List<Map<String, dynamic>> sections = getSectionsInOrder(parsedData);
    
    for (Map<String, dynamic> section in sections) {
      Map<String, dynamic> sectionObject = {
        'sectionNumber': section['number'],
        'sectionTitle': section['title'],
        'section': section,
        'beforeContent': section['beforeContent'] ?? [],
        'afterContent': section['afterContent'] ?? [],
        'order': section['order'],
      };
      sectionObjects.add(sectionObject);
    }
    
    return sectionObjects;
  }

  /// Splits text by numbered section pattern "\\n\\n{number}. **"
  /// Returns a list where first element is content before first section,
  /// and subsequent elements are the sections with their content
  static List<Map<String, dynamic>> splitByNumberedSections(String responseText) {
    try {
      // Clean the response text by removing escape characters and extra quotes
      String cleanedText = responseText
          .replaceAll('\\"', '"')
          .replaceAll('\\n', '\n')
          .trim();
      
      // If the text starts and ends with quotes, remove them
      if (cleanedText.startsWith('"') && cleanedText.endsWith('"')) {
        cleanedText = cleanedText.substring(1, cleanedText.length - 1);
      }

      List<Map<String, dynamic>> splitSections = [];
      
      // Pattern to match numbered sections: \n\n{number}. **
      RegExp sectionPattern = RegExp(r'\n\n(\d+)\.\s*\*\*');
      
      // Find all matches
      Iterable<RegExpMatch> matches = sectionPattern.allMatches(cleanedText);
      List<RegExpMatch> matchList = matches.toList();
      
      if (matchList.isEmpty) {
        // No numbered sections found, return the entire text as before content
        splitSections.add({
          'type': 'before_content',
          'content': cleanedText,
          'sectionNumber': null,
          'startIndex': 0,
          'endIndex': cleanedText.length,
        });
        return splitSections;
      }
      
      // Add content before first section if it exists
      int firstSectionStart = matchList.first.start;
      if (firstSectionStart > 0) {
        String beforeContent = cleanedText.substring(0, firstSectionStart).trim();
        if (beforeContent.isNotEmpty) {
          splitSections.add({
            'type': 'before_content',
            'content': beforeContent,
            'sectionNumber': null,
            'startIndex': 0,
            'endIndex': firstSectionStart,
          });
        }
      }
      
      // Process each section
      for (int i = 0; i < matchList.length; i++) {
        RegExpMatch currentMatch = matchList[i];
        String sectionNumber = currentMatch.group(1) ?? '';
        
        // Determine the end of this section (start of next section or end of text)
        int sectionStart = currentMatch.start;
        int sectionEnd = (i < matchList.length - 1) 
            ? matchList[i + 1].start 
            : cleanedText.length;
        
        // Extract section content
        String sectionContent = cleanedText.substring(sectionStart, sectionEnd).trim();
        
        // Remove the leading \n\n from section content
        if (sectionContent.startsWith('\n\n')) {
          sectionContent = sectionContent.substring(2);
        }
        
        splitSections.add({
          'type': 'section',
          'content': sectionContent,
          'sectionNumber': sectionNumber,
          'startIndex': sectionStart,
          'endIndex': sectionEnd,
          'rawMatch': currentMatch.group(0),
        });
      }
      
      return splitSections;
      
    } catch (e) {
      // Return error information if splitting fails
      return [{
        'type': 'error',
        'content': 'Failed to split text: $e',
        'sectionNumber': null,
        'startIndex': 0,
        'endIndex': 0,
      }];
    }
  }

  /// Splits text and returns only the content parts (without metadata)
  static List<String> splitByNumberedSectionsSimple(String responseText) {
    List<Map<String, dynamic>> splitResult = splitByNumberedSections(responseText);
    List<String> contentParts = [];
    
    for (Map<String, dynamic> part in splitResult) {
      if (part['type'] != 'error') {
        if(!part['content'].startsWith('1. **') && !part['content'].startsWith('4. **')) {
          // RegExp to match content between ** **
          RegExp pattern = RegExp(r'\d+\.\s*\*\*.*?\*\*');

          // Replace the matched pattern with an empty string
          String output = part['content'].replaceAll(pattern, '');
          contentParts.add(output ?? '');
        }
      }
    }
    
    return contentParts;
  }

  /// Gets content before first numbered section
  static String getContentBeforeFirstSection(String responseText) {
    List<Map<String, dynamic>> splitResult = splitByNumberedSections(responseText);
    
    for (Map<String, dynamic> part in splitResult) {
      if (part['type'] == 'before_content') {
        return part['content'] ?? '';
      }
    }
    
    return '';
  }

  /// Gets all numbered sections with their content
  static List<Map<String, dynamic>> getNumberedSections(String responseText) {
    List<Map<String, dynamic>> splitResult = splitByNumberedSections(responseText);
    
    return splitResult.where((part) => part['type'] == 'section').toList();
  }

  static String generateFinalContent(List<String> parsedData) {
   String finalContent = '';
    for (int i=0;i<parsedData.length;i++) {
      if(i!=1 &&i!=3) {
        finalContent += "${parsedData[i]}\n";
      }else if(i==3){
        finalContent += parsedData[i].replaceAllMapped("\n\n", (match) => "\n",);
      }
    }
    return finalContent;
  }


}
